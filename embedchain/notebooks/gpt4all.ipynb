{"cells": [{"cell_type": "markdown", "metadata": {"id": "b02n_zJ_hl3d"}, "source": ["## Cookbook for using GPT4All with Embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "gyJ6ui2vhtMY"}, "source": ["### Step-1: Install embedchain package"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-NbXjAdlh0vJ", "outputId": "077fa470-b51f-4c29-8c22-9c5f0a9cef47"}, "outputs": [], "source": ["!pip install embedchain[opensource]"]}, {"cell_type": "markdown", "metadata": {"id": "nGnpSYAAh2bQ"}, "source": ["### Step-2: Set GPT4ALL related environment variables\n", "\n", "GPT4All is free for all and doesn't require any API Key to use it. So you can use it for free!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0fBdQ9GAiRvK"}, "outputs": [], "source": ["from embedchain import App"]}, {"cell_type": "markdown", "metadata": {"id": "PGt6uPLIi1CS"}, "source": ["### Step-3 Create embedchain app and define your config"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Amzxk3m-i3tD", "outputId": "775db99b-e217-47db-f87f-788495d86f26"}, "outputs": [], "source": ["app = App.from_config(config={\n", "    \"llm\": {\n", "        \"provider\": \"gpt4all\",\n", "        \"config\": {\n", "            \"model\": \"orca-mini-3b-gguf2-q4_0.gguf\",\n", "            \"temperature\": 0.5,\n", "            \"max_tokens\": 1000,\n", "            \"top_p\": 1,\n", "            \"stream\": False\n", "        }\n", "    },\n", "    \"embedder\": {\n", "        \"provider\": \"gpt4all\",\n", "        \"config\": {\n", "            \"model\": \"all-MiniLM-L6-v2\"\n", "        }\n", "    }\n", "})"]}, {"cell_type": "markdown", "metadata": {"id": "XNXv4yZwi7ef"}, "source": ["### Step-4: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "Sn_0rx9QjIY9", "outputId": "c6514f17-3cb2-4fbc-c80d-79b3a311ff30"}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {"id": "_7W6fDeAjMAP"}, "source": ["### Step-5: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 480}, "id": "cvIK7dWRjN_f", "outputId": "c74f356a-d2fb-426d-b36c-d84911397338"}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}