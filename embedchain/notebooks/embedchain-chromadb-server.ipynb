{"cells": [{"cell_type": "markdown", "id": "553f2e71", "metadata": {}, "source": ["## Embedchain chromadb server example"]}, {"cell_type": "markdown", "id": "513e12e6", "metadata": {}, "source": ["This notebook shows an example of how you can use embedchain with chromdb (server). \n", "\n", "\n", "First, run chroma inside docker using the following command:\n", "\n", "\n", "```bash\n", "git clone https://github.com/chroma-core/chroma\n", "cd chroma && docker-compose up -d --build\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "92e7ad71", "metadata": {}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "from embedchain.config import AppConfig\n", "\n", "\n", "chromadb_host = \"localhost\"\n", "chromadb_port = 8000\n", "\n", "config = AppConfig(host=chromadb_host, port=chromadb_port)\n", "elon_bot = App(config)"]}, {"cell_type": "code", "execution_count": 2, "id": "1a6d6841", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All data from https://en.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON> already exists in the database.\n", "All data from https://www.tesla.com/elon-musk already exists in the database.\n"]}], "source": ["# Embed Online Resources\n", "elon_bot.add(\"web_page\", \"https://en.wikipedia.org/wiki/Elon_Musk\")\n", "elon_bot.add(\"web_page\", \"https://www.tesla.com/elon-musk\")"]}, {"cell_type": "code", "execution_count": 3, "id": "34cda99c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Elon Musk runs four companies: Tesla, SpaceX, Neuralink, and The Boring Company.'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["elon_bot.query(\"How many companies does Elon Musk run?\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}