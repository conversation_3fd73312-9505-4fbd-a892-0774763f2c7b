{"cells": [{"cell_type": "markdown", "metadata": {"id": "b02n_zJ_hl3d"}, "source": ["## Cookbook for using Hugging Face Hub with Embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "gyJ6ui2vhtMY"}, "source": ["### Step-1: Install embedchain package"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "-NbXjAdlh0vJ", "outputId": "35ddc904-8067-44cf-dcc9-3c8b4cd29989"}, "outputs": [], "source": ["!pip install embedchain[huggingface_hub,opensource]"]}, {"cell_type": "markdown", "metadata": {"id": "nGnpSYAAh2bQ"}, "source": ["### Step-2: Set Hugging Face Hub related environment variables\n", "\n", "You can find your `HUGGINGFACE_ACCESS_TOKEN` key on your [Hugging Face Hub dashboard](https://huggingface.co/settings/tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0fBdQ9GAiRvK"}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "\n", "os.environ[\"HUGGINGFACE_ACCESS_TOKEN\"] = \"hf_xxx\""]}, {"cell_type": "markdown", "metadata": {"id": "PGt6uPLIi1CS"}, "source": ["### Step-3 Create embedchain app and define your config"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Amzxk3m-i3tD"}, "outputs": [], "source": ["app = App.from_config(config={\n", "    \"llm\": {\n", "        \"provider\": \"huggingface\",\n", "        \"config\": {\n", "            \"model\": \"google/flan-t5-xxl\",\n", "            \"temperature\": 0.5,\n", "            \"max_tokens\": 1000,\n", "            \"top_p\": 0.8,\n", "            \"stream\": False\n", "        }\n", "    },\n", "    \"embedder\": {\n", "        \"provider\": \"huggingface\",\n", "        \"config\": {\n", "            \"model\": \"sentence-transformers/all-mpnet-base-v2\"\n", "        }\n", "    }\n", "})"]}, {"cell_type": "markdown", "metadata": {"id": "XNXv4yZwi7ef"}, "source": ["### Step-4: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 70}, "id": "Sn_0rx9QjIY9", "outputId": "3c2a803a-3a93-4b0d-a6ae-17ae3c96c3c2"}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {"id": "_7W6fDeAjMAP"}, "source": ["### Step-5: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cvIK7dWRjN_f", "outputId": "47a89d1c-b322-495c-822a-6c2ecef894d2"}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}