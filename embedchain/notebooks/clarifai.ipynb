{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Cookbook for using Clarifai LLM and Embedders with Embedchain"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step-1: Install embedchain-clarifai package"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install embedchain[clarifai]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step-2: Set Clarifai PAT as env variable.\n", "Sign-up to [Clarifai](https://clarifai.com/signup?utm_source=clarifai_home&utm_medium=direct&) platform and you can obtain `CLARIFAI_PAT` by following this [link](https://docs.clarifai.com/clarifai-basics/authentication/personal-access-tokens/).\n", "\n", "optionally you can also pass `api_key` in config of llm/embedder class."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "\n", "os.environ[\"CLARIFAI_PAT\"]=\"xxx\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step-3 Create embedchain app using clarifai LLM and embedder and define your config.\n", "\n", "Browse through Clarifai community page to get the URL of different [LLM](https://clarifai.com/explore/models?page=1&perPage=24&filterData=%5B%7B%22field%22%3A%22use_cases%22%2C%22value%22%3A%5B%22llm%22%5D%7D%5D) and [embedding](https://clarifai.com/explore/models?page=1&perPage=24&filterData=%5B%7B%22field%22%3A%22input_fields%22%2C%22value%22%3A%5B%22text%22%5D%7D%2C%7B%22field%22%3A%22output_fields%22%2C%22value%22%3A%5B%22embeddings%22%5D%7D%5D) models available."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Use model_kwargs to pass all model specific parameters for inference.\n", "app = App.from_config(config={\n", "    \"llm\": {\n", "        \"provider\": \"clarifai\",\n", "        \"config\": {\n", "            \"model\": \"https://clarifai.com/mistralai/completion/models/mistral-7B-Instruct\",\n", "            \"model_kwargs\": {\n", "            \"temperature\": 0.5,\n", "            \"max_tokens\": 1000\n", "            }\n", "        }\n", "    },\n", "    \"embedder\": {\n", "        \"provider\": \"clarifai\",\n", "        \"config\": {\n", "            \"model\": \"https://clarifai.com/openai/embed/models/text-embedding-ada\",\n", "        }\n", "}\n", "})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step-4: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step-5: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"kernelspec": {"display_name": "v1", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.10"}}, "nbformat": 4, "nbformat_minor": 2}