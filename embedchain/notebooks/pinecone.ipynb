{"cells": [{"cell_type": "markdown", "metadata": {"id": "b02n_zJ_hl3d"}, "source": ["## Cookbook for using PineconeDB with Embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "gyJ6ui2vhtMY"}, "source": ["### Step-1: Install embedchain package"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-NbXjAdlh0vJ"}, "outputs": [], "source": ["!pip install embedchain pinecone-client pinecone-text"]}, {"cell_type": "markdown", "metadata": {"id": "nGnpSYAAh2bQ"}, "source": ["### Step-2: Set environment variables needed for Pinecone\n", "\n", "You can find this env variable on your [OpenAI dashboard](https://platform.openai.com/account/api-keys) and [Pinecone dashboard](https://app.pinecone.io/)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0fBdQ9GAiRvK"}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-xxx\"\n", "os.environ[\"PINECONE_API_KEY\"] = \"xxx\"\n", "os.environ[\"PINECONE_ENV\"] = \"xxx\""]}, {"cell_type": "markdown", "metadata": {"id": "PGt6uPLIi1CS"}, "source": ["### Step-3 Create embedchain app and define your config"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Amzxk3m-i3tD"}, "outputs": [], "source": ["app = App.from_config(config={\n", "    \"provider\": \"pinecone\",\n", "    \"config\": {\n", "        \"metric\": \"cosine\",\n", "        \"vector_dimension\": 768,\n", "        \"collection_name\": \"pc-index\"\n", "    }\n", "})"]}, {"cell_type": "markdown", "metadata": {"id": "XNXv4yZwi7ef"}, "source": ["### Step-4: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Sn_0rx9QjIY9"}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {"id": "_7W6fDeAjMAP"}, "source": ["### Step-5: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cvIK7dWRjN_f"}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}