{"cells": [{"cell_type": "markdown", "id": "63ab5e89", "metadata": {}, "source": ["## Cookbook for using Azure OpenAI with Embedchain"]}, {"cell_type": "markdown", "id": "e32a0265", "metadata": {}, "source": ["### Step-1: Install embedchain package"]}, {"cell_type": "code", "execution_count": null, "id": "b80ff15a", "metadata": {}, "outputs": [], "source": ["!pip install embedchain"]}, {"cell_type": "markdown", "id": "ac982a56", "metadata": {}, "source": ["### Step-2: Set Azure OpenAI related environment variables\n", "\n", "You can find these env variables on your Azure OpenAI dashboard."]}, {"cell_type": "code", "execution_count": null, "id": "e0a36133", "metadata": {}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "\n", "os.environ[\"OPENAI_API_TYPE\"] = \"azure\"\n", "os.environ[\"OPENAI_API_BASE\"] = \"https://xxx.openai.azure.com/\"\n", "os.environ[\"OPENAI_API_KEY\"] = \"xxx\"\n", "os.environ[\"OPENAI_API_VERSION\"] = \"xxx\""]}, {"cell_type": "markdown", "id": "7d7b554e", "metadata": {}, "source": ["### Step-3: Define your llm and embedding model config"]}, {"cell_type": "code", "execution_count": null, "id": "b9f52fc5", "metadata": {}, "outputs": [], "source": ["config = \"\"\"\n", "llm:\n", "  provider: azure_openai\n", "  model: gpt-35-turbo\n", "  config:\n", "    deployment_name: ec_openai_azure\n", "    temperature: 0.5\n", "    max_tokens: 1000\n", "    top_p: 1\n", "    stream: false\n", "\n", "embedder:\n", "  provider: azure_openai\n", "  config:\n", "    model: text-embedding-ada-002\n", "    deployment_name: ec_embeddings_ada_002\n", "\"\"\"\n", "\n", "# Write the multi-line string to a YAML file\n", "with open('azure_openai.yaml', 'w') as file:\n", "    file.write(config)"]}, {"cell_type": "markdown", "id": "98a11130", "metadata": {}, "source": ["### Step-4 Create embedchain app based on the config"]}, {"cell_type": "code", "execution_count": null, "id": "1ee9bdd9", "metadata": {}, "outputs": [], "source": ["app = App.from_config(config_path=\"azure_openai.yaml\")"]}, {"cell_type": "markdown", "id": "554dc97b", "metadata": {}, "source": ["### Step-5: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "id": "686ae765", "metadata": {}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "id": "ccc7d421", "metadata": {}, "source": ["### Step-6: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "id": "27868a7d", "metadata": {}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}